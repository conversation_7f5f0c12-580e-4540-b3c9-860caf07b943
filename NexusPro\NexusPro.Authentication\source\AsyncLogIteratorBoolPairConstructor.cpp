﻿/*
 * AsyncLogIteratorBoolPairConstructor.cpp
 * Original Function: ??0?$std::pair@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@_N@std@@@AEBV?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@1@AEB_N@Z
 * Address: 0x1403C43F0
 *
 * STL pair constructor for async log iterator and boolean.
 * Initializes pair with iterator and boolean value.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Constructor for pair with iterator and boolean
 * Initializes pair with iterator and boolean value
 * @param this_ptr Pointer to the pair object being constructed
 * @param _Val1 Reference to the iterator value for first element
 * @param _Val2 Reference to the boolean value for second element
 */
void AsyncLogIteratorBoolPairConstructor(void* this_ptr, void* _Val1, const bool* _Val2)
{
    __int64* v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-28h]@1
    void** v6; // [sp+30h] [bp+8h]@1
    const bool* v7; // [sp+40h] [bp+18h]@1

    v7 = _Val2;
    v6 = (void**)this_ptr;
    v3 = &v5;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v3 = 0xCCCCCCCC;
        v3 = (__int64*)((char*)v3 + 4);
    }

    // Initialize first element with iterator
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::_Iterator(
        &v6->first,
        _Val1);

    // Initialize second element with boolean value
    v6->second = *v7;
}





