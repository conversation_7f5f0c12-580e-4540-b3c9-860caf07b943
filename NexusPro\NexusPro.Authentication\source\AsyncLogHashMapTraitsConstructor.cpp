﻿/*
 * AsyncLogHashMapTraitsConstructor.cpp
 * Original Function: ??0?$_Hmap_traits@HPEAVCAsyncLogInfo@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@$0A@@stdext@@@AEBV?$hash_compare@HU?$less@H@std@@@1@@Z
 * Address: 0x1403C4520
 *
 * Hash map traits constructor for async log containers.
 * Initializes hash map traits with comparison function for async log info pairs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>
#include <utility>

/**
 * Constructor for hash map traits template specialization
 * Initializes hash map traits with comparison function
 * @param this_ptr Pointer to the hash map traits object being constructed
 * @param _Traits Pointer to the hash comparison traits
 */
void AsyncLogHashMapTraitsConstructor(void* this_ptr, stdext::hash_compare<int, std::less<int>>* _Traits)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>* v5; // [sp+30h] [bp+8h]@1
    stdext::hash_compare<int, std::less<int>>* v6; // [sp+38h] [bp+10h]@1

    v6 = _Traits;
    v5 = (stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>*)this_ptr;
    v2 = &v4;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    // Initialize container base for iterator management
    std::_Container_base::_Container_base((std::_Container_base*)&v5->_Myfirstiter);

    // Copy comparison function from traits parameter
    v5->comp = (stdext::hash_compare<int, std::less<int>>)v6->comp;
}




