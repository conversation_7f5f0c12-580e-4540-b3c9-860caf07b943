﻿/*
 * AsyncLogListIteratorNodeConstructor.cpp
 * Original Function: ??0?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@PEAU_Node@?$_List_nod@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@2@@Z
 * Address: 0x1403C5B30
 *
 * STL list iterator constructor with node parameter for async log containers.
 * Initializes iterator with specific list node for async log info pairs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Constructor for list iterator with node parameter
 * Initializes iterator pointing to specific list node
 * @param this_ptr Pointer to the iterator object being constructed
 * @param _Pnode Pointer to the list node to initialize iterator with
 */
void AsyncLogListIteratorNodeConstructor(void* this_ptr, std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node* _Pnode)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    void** v5; // [sp+30h] [bp+8h]@1

    v5 = (void**)this_ptr;
    v2 = &v4;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    // Initialize iterator with container and node
    std::_Iterator<0>::_Iterator<0>(
        (std::_Iterator<0>*)&v5->_Mycont,
        _Pnode);
}




