﻿/*
 * ValidateECPGroupParameters.cpp
 * Original Function: CryptoPP::DL_GroupParameters<ECPPoint>::Validate
 * Original Address: 0x14046A920
 *
 * Description: Validates ECP (Elliptic Curve over Prime field) group parameters.
 * Performs cryptographic validation of elliptic curve parameters for security.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Validates ECP elliptic curve group parameters
// Returns true if parameters are valid, false otherwise
bool CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::Validate(
    CryptoPP::RandomNumberGenerator& rng,
    unsigned int level) const {
    // Initialize debug pattern for stack frame
    char debugBuffer[96];
    memset(debugBuffer, 0xCC, sizeof(debugBuffer));

    // Get the elliptic curve implementation
    const CryptoPP::EllipticCurve* curve = this->GetCurve();
    if (!curve) {
        return false;
    }

    // Check if curve is valid
    if (!curve->IsValid()) {
        return false;
    }

    // Check if validation level is already satisfied
    if (this->m_validationLevel > level) {
        return true;
    }

    // Perform curve parameter validation
    bool curveValid = curve->ValidateParameters(rng, level);

    if (curveValid) {
        // Validate the base point and order
        const CryptoPP::ECPPoint& basePoint = this->GetBasePoint();
        const CryptoPP::Integer& order = this->GetOrder();

        bool basePointValid = curve->ValidatePoint(basePoint, rng, level);

        if (basePointValid) {
            // Update validation level on success
            this->m_validationLevel = level + 1;
        } else {
            this->m_validationLevel = 0;
        }

        return basePointValid;
    }

    // Validation failed
    this->m_validationLevel = 0;
    return false;
}
}




