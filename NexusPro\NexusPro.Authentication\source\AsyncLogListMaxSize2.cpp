﻿/*
 * AsyncLogListMaxSize2.cpp
 * Original Function: ?max_size@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@QEBA_KXZ
 * Address: 0x1403C5F60
 *
 * STL list max_size function for async log containers.
 * Returns the maximum number of elements that can be stored in the list.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <list>
#include <memory>
#include <utility>

/**
 * Returns the maximum size for async log list
 * Delegates to the underlying allocator's max_size function
 * @param this_ptr Pointer to the list object
 * @return Maximum number of elements that can be stored
 */
unsigned __int64 AsyncLogListMaxSize2(std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* this_ptr)
{
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* v5; // [sp+30h] [bp+8h]@1

    v5 = this_ptr;
    v1 = &v4;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }

    // Return maximum size from underlying allocator
    return std::allocator<std::pair<int const, CAsyncLogInfo*>>::max_size(&v5->_Alval);
}




