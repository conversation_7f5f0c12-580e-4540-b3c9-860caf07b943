﻿/*
 * AsyncLogMakePair.cpp
 * Original Function: ??$make_pair@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@std@@YA?AU?$std::pair@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@0@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@Z
 * Address: 0x1403C75D0
 *
 * STL make_pair function for async log type and info pairs.
 * Creates a pair containing async log type enum and async log info pointer.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <utility>

/**
 * Creates a pair from async log type and async log info pointer
 * Template specialization of std::make_pair for async log containers
 * @param result Pointer to the result pair object
 * @param _Val1 Async log type enum value
 * @param _Val2 Pointer to async log info object
 * @return Pointer to the constructed pair
 */
std::pair<enum ASYNC_LOG_TYPE, CAsyncLogInfo*>* AsyncLogMakePair(std::pair<enum ASYNC_LOG_TYPE, CAsyncLogInfo*>* result, ASYNC_LOG_TYPE _Val1, CAsyncLogInfo* _Val2)
{
    __int64* v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v6; // [sp+0h] [bp-28h]@1
    std::pair<enum ASYNC_LOG_TYPE, CAsyncLogInfo*>* v7; // [sp+30h] [bp+8h]@1
    ASYNC_LOG_TYPE _Val1a; // [sp+38h] [bp+10h]@1
    CAsyncLogInfo* _Val2a; // [sp+40h] [bp+18h]@1

    _Val2a = _Val2;
    _Val1a = _Val1;
    v7 = result;
    v3 = &v6;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v3 = 0xCCCCCCCC;
        v3 = (__int64*)((char*)v3 + 4);
    }

    // Construct the pair with the provided values
    std::pair<enum ASYNC_LOG_TYPE, CAsyncLogInfo*>::pair(v7, &_Val1a, &_Val2a);
    return v7;
}




