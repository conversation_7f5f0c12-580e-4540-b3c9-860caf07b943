﻿/*
 * MoveMapLimitInfoDestructor2.cpp
 * Original Function: CMoveMapLimitInfoList destructor helper 2
 * Original Address: 0x1403A5CF0
 *
 * Description: Destructor helper function for CMoveMapLimitInfoList.
 * Cleans up vector iterator resources during exception unwinding.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <memory>
#include <vector>

// Destructor helper function for CMoveMapLimitInfoList
// Cleans up vector iterator at offset 168 during exception unwinding
void CMoveMapLimitInfoList::DestructorHelper2(void* exceptionFrame, void* objectPtr) {
    // Clean up vector iterator at offset 168
    std::vector<CMoveMapLimitInfo*>::iterator* iterPtr =
        reinterpret_cast<std::vector<CMoveMapLimitInfo*>::iterator*>(
            reinterpret_cast<char*>(objectPtr) + 168);

    // Call destructor for vector iterator
    iterPtr->~iterator();
}




