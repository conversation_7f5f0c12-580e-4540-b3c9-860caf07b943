﻿/*
 * AsyncLogListErase2.cpp
 * Original Function: ?erase@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@?AV?$_Iterator@$0A@@12@V312@@Z
 * Address: 0x1403C5FB0
 *
 * STL list erase function for async log containers.
 * Removes a single element from the list at the specified iterator position.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>

/**
 * Erases a single element from the async log list
 * Removes the element at the specified iterator position and returns iterator to next element
 * @param result Pointer to store the result iterator
 * @param this_ptr Pointer to the list object
 * @param _Where Iterator pointing to the element to erase
 * @return Iterator to the element following the erased element
 */
void* AsyncLogListErase2(void* result, std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* this_ptr, void* _Where)
{
    __int64* v3; // rdi@1
    signed __int64 i; // rcx@1
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node* v5; // rdx@4
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node* v6; // rdx@5
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node** v7; // rax@5
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node* v8; // rdx@5
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node** v9; // rax@5
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node* v10; // rdx@5
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node* v11; // rdx@5
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node** v12; // rax@5
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node* v13; // rdx@5
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node** v14; // rax@5
    __int64 v16; // [sp+0h] [bp-78h]@1
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* v17; // [sp+20h] [bp-58h]@4
    void* resulta; // [sp+28h] [bp-50h]@4
    int v19; // [sp+40h] [bp-38h]@4
    __int64 v20; // [sp+48h] [bp-30h]@4
    void** v21; // [sp+50h] [bp-28h]@4
    void** v22; // [sp+58h] [bp-20h]@4
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node** v23; // [sp+60h] [bp-18h]@5
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node** v24; // [sp+68h] [bp-10h]@5
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* v25; // [sp+80h] [bp+8h]@1
    void** v26; // [sp+88h] [bp+10h]@1
    void** __that; // [sp+90h] [bp+18h]@1

    __that = (void**)_Where;
    v26 = (void**)result;
    v25 = this_ptr;
    v3 = &v16;

    // Initialize debug pattern in local memory
    for (i = 28LL; i; --i) {
        *(DWORD*)v3 = 0xCCCCCCCC;
        v3 = (__int64*)((char*)v3 + 4);
    }
    v20 = -2LL;
    v19 = 0;

    // Get iterator to next element before erasing current
    v21 = std::_Iterator<0>::operator++(_Where, &resulta, 0);
    v22 = (void**)v21;

    // Get the node to be erased
    v17 = (std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)__that->_Mycont;
    std::_Iterator<0>::_Orphan_me(&resulta);

    // Only erase if not pointing to head node
    if (v17 != (std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)v25->_Myhead) {
        // Unlink the node from the list by updating next/prev pointers
        v23 = std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Nextnode(v17, v5);
        v7 = std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Prevnode(v17, v6);
        v9 = std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Nextnode(
            (std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)*v7, v8);
        *v9 = *v23;

        v24 = std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Prevnode(v17, v10);
        v12 = std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Nextnode(v17, v11);
        v14 = std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Prevnode(
            (std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)*v12, v13);
        *v14 = *v24;

        // Destroy and deallocate the node
        std::allocator<std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node>::destroy(
            &v25->_Alnod,
            (std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node*)v17);
        std::allocator<std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node>::deallocate(
            &v25->_Alnod,
            (std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node*)v17,
            1ui64);
        --v25->_Mysize;
    }

    // Construct result iterator
    std::_Iterator<0>::_Iterator<0>(v26, __that);
    v19 |= 1u;
    std::_Iterator<0>::~_Iterator<0>(__that);
    return v26;
}





