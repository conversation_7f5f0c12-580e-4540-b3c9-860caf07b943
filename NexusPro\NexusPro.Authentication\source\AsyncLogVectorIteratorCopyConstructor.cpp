﻿/*
 * AsyncLogVectorIteratorCopyConstructor.cpp
 * Original Function: std::_Vector_iterator copy constructor for AsyncLogInfo list iterators
 * Original Address: 0x1403C5DC0
 *
 * Description: Copy constructor for vector iterator containing list iterators.
 * Initializes random access iterator base and copies pointer.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>
#include <vector>
#include <cstring>

// Copy constructor for vector iterator containing list iterators
// Initializes iterator from another iterator of the same type
template<typename T, typename Allocator>
std::_Vector_iterator<T, Allocator>::_Vector_iterator(const std::_Vector_iterator<T, Allocator>& other) {
    // Initialize debug pattern for stack frame
    char debugBuffer[32];
    memset(debug<PERSON>uffer, 0xCC, sizeof(debugBuffer));

    // Initialize random access iterator base
    std::_Ranit<T, ptrdiff_t, T*, T&>::_Ranit(other);

    // Copy the pointer from other iterator
    this->_Ptr = other._Ptr;
}





