﻿/*
 * AsyncLogVectorConstIteratorEquality.cpp
 * Original Function: ??8?$_Vector_const_iterator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@QEBA_NAEBV01@@Z
 * Address: 0x1403C7460
 *
 * STL vector const iterator equality operator for async log containers.
 * Compares two vector const iterators for equality.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Equality operator for vector const iterator
 * Compares two vector const iterators for equality
 * @param this_ptr Pointer to the first iterator
 * @param _Right Pointer to the second iterator
 * @return true if iterators point to the same element
 */
bool AsyncLogVectorConstIteratorEquality(
    const std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::_Vector_const_iterator* this_ptr,
    const std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::_Vector_const_iterator* _Right)
{
    int* v2; // rdi@1
    signed __int64 i; // rcx@1
    int v5; // [sp+0h] [bp-18h]@1
    void** v6; // [sp+20h] [bp+8h]@1

    v6 = (void**)this_ptr;
    v2 = &v5;

    // Initialize debug pattern in local memory
    for (i = 4LL; i; --i) {
        *v2 = 0xCCCCCCCC;
        ++v2;
    }

    // Compare iterator pointers
    return v6->_Myptr == ((void**)_Right)->_Myptr;
}





