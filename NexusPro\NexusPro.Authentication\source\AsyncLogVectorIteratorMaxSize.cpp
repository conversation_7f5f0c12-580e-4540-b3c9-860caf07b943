﻿/*
 * AsyncLogVectorIteratorMaxSize.cpp
 * Original Function: ?max_size@?$std::vector@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@QEBA_KXZ
 * Address: 0x1403C69A0
 *
 * STL vector max_size function for async log iterator containers.
 * Returns the maximum number of iterator elements that can be stored in the vector.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>

/**
 * Returns the maximum size for async log vector iterator container
 * Delegates to the underlying allocator's max_size function
 * @param this_ptr Pointer to the vector object
 * @return Maximum number of iterator elements that can be stored
 */
unsigned __int64 AsyncLogVectorIteratorMaxSize(std::vector<std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>>>* this_ptr)
{
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    std::vector<std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>>>* v5; // [sp+30h] [bp+8h]@1

    v5 = this_ptr;
    v1 = &v4;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }

    // Return maximum size from underlying allocator
    return std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>>::max_size(&v5->_Alval);
}




