﻿/*
 * AsyncLogInfoConstructorJump.cpp
 * Original Function: j_??0CAsyncLogInfo@@@XZ
 * Address: 0x14000E4F8
 *
 * Jump table function for async log info constructor.
 * Redirects to the actual constructor implementation.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * Jump table function for async log info constructor
 * Redirects to the actual constructor implementation
 * @param this_ptr Pointer to the async log info object being constructed
 */
void AsyncLogInfoConstructorJump(CAsyncLogInfo* this_ptr)
{
    CAsyncLogInfo::CAsyncLogInfo(this_ptr);
}



