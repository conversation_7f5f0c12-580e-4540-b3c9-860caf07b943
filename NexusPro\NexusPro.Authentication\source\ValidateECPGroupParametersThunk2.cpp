﻿/*
 * ValidateECPGroupParametersThunk2.cpp
 * Original Function: ?Validate@?$DL_GroupParameters@UECPPoint@CryptoPP@@@CryptoPP@@$4PPPPPPPM@BFA@EBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x14046AD80
 *
 * Virtual function thunk for ECP group parameter validation.
 * Adjusts this pointer and calls the actual validation function for elliptic curve point parameters.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * Virtual function thunk for ECP group parameter validation
 * Performs this pointer adjustment and delegates to actual validation function
 * @param a1 Adjusted this pointer for the DL_GroupParameters object
 * @param a2 Random number generator for validation
 * @param a3 Validation level flags
 * @return true if group parameters are valid, false otherwise
 */
bool ValidateECPGroupParametersThunk2(__int64 a1, CryptoPP::RandomNumberGenerator* a2, unsigned int a3)
{
    // Adjust this pointer using virtual table offset and call actual validation
    return CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::Validate(
        (CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>*)(a1 - *((DWORD*)a1 - 4) - 336),
        a2,
        a3);
}



