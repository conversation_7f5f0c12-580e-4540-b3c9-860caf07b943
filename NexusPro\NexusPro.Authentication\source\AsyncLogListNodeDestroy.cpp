﻿/*
 * AsyncLogListNodeDestroy.cpp
 * Original Function: ??$_Destroy@PEAU_Node@?$_List_nod@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@std@@YAXPEAPEAU_Node@?$_List_nod@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@0@@Z
 * Address: 0x1403C7BC0
 *
 * STL destroy function for async log list node pointers.
 * Destroys list node pointer for async log info pairs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>
#include <utility>

/**
 * Destroys a list node pointer
 * Template specialization for async log list node destruction
 * @param _Ptr Pointer to the list node pointer to destroy
 */
void AsyncLogListNodeDestroy(std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node** _Ptr)
{
    // Empty destructor - pointer destruction is trivial
    // No actual work needed for pointer destruction
}




