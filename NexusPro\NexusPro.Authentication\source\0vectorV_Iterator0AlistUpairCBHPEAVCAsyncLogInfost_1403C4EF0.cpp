/*
 * AsyncLogVectorFillConstructor.cpp
 * Original Function: ??0?$std::vector@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@@_KAEBV?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@1@AEBV?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@1@@Z
 * Address: 0x1403C4EF0
 *
 * STL vector fill constructor for async log iterator containers.
 * Initializes vector with specified count of elements and allocator.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Fill constructor for vector with count, value and allocator
 * Initializes vector with specified count of elements
 * @param this_ptr Pointer to the vector object being constructed
 * @param _Count Number of elements to initialize
 * @param _Val Reference to the value to fill with
 * @param _Al Reference to the allocator to use
 */
void AsyncLogVectorFillConstructor(void* this_ptr, unsigned __int64 _Count, void* _Val, void* _Al)
{
    __int64* v4; // rdi@1
    signed __int64 i; // rcx@1
    void* v6; // al@4
    __int64 v7; // [sp+0h] [bp-38h]@1
    char v8; // [sp+20h] [bp-18h]@4
    void** v9; // [sp+28h] [bp-10h]@4
    void** v10; // [sp+40h] [bp+8h]@1
    unsigned __int64 _Counta; // [sp+48h] [bp+10h]@1
    void** _Vala; // [sp+50h] [bp+18h]@1

    _Vala = (void**)_Val;
    _Counta = _Count;
    v10 = (void**)this_ptr;
    v4 = &v7;

    // Initialize debug pattern in local memory
    for (i = 12LL; i; --i) {
        *(DWORD*)v4 = 0xCCCCCCCC;
        v4 = (__int64*)((char*)v4 + 4);
    }

    v9 = (void**)&v8;

    // Initialize temporary allocator
    std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::allocator(
        (std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>*)&v8,
        (std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>*)_Al);

    // Initialize vector value with allocator
    std::_Vector_val<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>::_Vector_val(
        (std::_Vector_val<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>*)&v10->_Myfirstiter,
        v6);

    // Construct n elements with specified value
    std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::_Construct_n(
        v10,
        _Counta,
        _Vala);
}




