﻿/*
 * MoveMapLimitInfoDestructor3.cpp
 * Original Function: CMoveMapLimitInfoList destructor helper 3
 * Original Address: 0x1403A5D20
 *
 * Description: Destructor helper function for CMoveMapLimitInfoList.
 * Cleans up vector const iterator resources during exception unwinding.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <memory>
#include <vector>

// Destructor helper function for CMoveMapLimitInfoList
// Cleans up vector const iterator at offset 104 during exception unwinding
void CMoveMapLimitInfoList::DestructorHelper3(void* exceptionFrame, void* objectPtr) {
    // Clean up vector const iterator at offset 104
    std::vector<CMoveMapLimitInfo*>::const_iterator* iterPtr =
        reinterpret_cast<std::vector<CMoveMapLimitInfo*>::const_iterator*>(
            reinterpret_cast<char*>(objectPtr) + 104);

    // Call destructor for vector const iterator
    iterPtr->~const_iterator();
}




