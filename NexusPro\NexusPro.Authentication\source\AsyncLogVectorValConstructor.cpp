﻿/*
 * AsyncLogVectorValConstructor.cpp
 * Original Function: ??0?$_Vector_val@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@IEAA@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@1@@Z
 * Address: 0x1403C6BE0
 *
 * STL vector value constructor for async log containers.
 * Initializes vector value with allocator for async log iterator containers.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Constructor for vector value with allocator
 * Initializes vector value with specified allocator
 * @param this_ptr Pointer to the vector value object being constructed
 * @param _Al Allocator for the vector value
 */
void AsyncLogVectorValConstructor(void* this_ptr, __int64 _Al)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    void** v5; // [sp+30h] [bp+8h]@1
    void** __formal; // [sp+38h] [bp+10h]@1

    __formal = (void**)_Al;
    v5 = (void**)this_ptr;
    v2 = &v4;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    // Initialize container base for iterator management
    std::_Container_base::_Container_base((std::_Container_base*)&v5->_Myfirstiter);

    // Initialize value allocator
    std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::allocator(
        &v5->_Alval,
        __formal);
}





