﻿/*
 * AsyncLogListConstIteratorEquality.cpp
 * Original Function: ??8?$_Const_iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@QEBA_NAEBV012@@Z
 * Address: 0x1403C2BE0
 *
 * STL list const iterator equality operator for async log containers.
 * Compares two const iterators for equality.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Equality operator for list const iterator
 * Compares two const iterators for equality
 * @param this_ptr Pointer to the first iterator
 * @param _Right Pointer to the second iterator
 * @return true if iterators point to the same element
 */
bool AsyncLogListConstIteratorEquality(
    const std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>* this_ptr,
    const std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>* _Right)
{
    int* v2; // rdi@1
    signed __int64 i; // rcx@1
    int v5; // [sp+0h] [bp-18h]@1
    void** v6; // [sp+20h] [bp+8h]@1

    v6 = (void**)this_ptr;
    v2 = &v5;

    // Initialize debug pattern in local memory
    for (i = 4LL; i; --i) {
        *v2 = 0xCCCCCCCC;
        ++v2;
    }

    // Compare iterator pointers
    return v6->_Ptr == ((void**)_Right)->_Ptr;
}





