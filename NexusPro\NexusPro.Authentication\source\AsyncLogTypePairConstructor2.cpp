﻿/*
 * AsyncLogTypePairConstructor2.cpp
 * Original Function: ??0?$std::pair@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@std@@@AEBW4ASYNC_LOG_TYPE@@AEBQEAVCAsyncLogInfo@@@Z
 * Address: 0x1403C8010
 *
 * STL pair constructor for async log type and info pointer.
 * Initializes pair with async log type and async log info pointer.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <utility>

/**
 * Constructor for pair with async log type and info pointer
 * Initializes pair with async log type and async log info pointer
 * @param this_ptr Pointer to the pair object being constructed
 * @param _Val1 Reference to the async log type for first element
 * @param _Val2 Reference to the async log info pointer for second element
 */
void AsyncLogTypePairConstructor2(std::pair<ASYNC_LOG_TYPE, CAsyncLogInfo*>* this_ptr, ASYNC_LOG_TYPE* _Val1, CAsyncLogInfo* const* _Val2)
{
    this_ptr->first = *_Val1;
    this_ptr->second = *_Val2;
}



