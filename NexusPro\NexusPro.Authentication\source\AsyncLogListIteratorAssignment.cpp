﻿/*
 * AsyncLogListIteratorAssignment.cpp
 * Original Function: ??4?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@AEAV012@AEBV012@@Z
 * Address: 0x1403C2CC0
 *
 * STL list iterator assignment operator for async log containers.
 * Assigns one iterator to another.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Assignment operator for list iterator
 * Assigns one iterator to another
 * @param this_ptr Pointer to the iterator object being assigned to
 * @param __that Pointer to the iterator object being assigned from
 * @return Pointer to the assigned iterator
 */
std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>*
AsyncLogListIteratorAssignment(
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>* this_ptr,
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>* __that)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-28h]@1
    void** v6; // [sp+30h] [bp+8h]@1

    v6 = (void**)this_ptr;
    v2 = &v5;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    // Assign const iterator base
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>::operator=(
        (std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>*)&v6->_Mycont,
        (std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>*)&__that->_Mycont);

    return (std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>*)v6;
}





