﻿/*
 * ValidateIntegerGroupParametersThunk.cpp
 * Original Function: ?Validate@?$DL_GroupParameters@VInteger@CryptoPP@@@CryptoPP@@$4PPPPPPPM@MA@EBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x1405AD4F0
 *
 * Virtual function thunk for integer-based group parameter validation.
 * Adjusts this pointer and calls the actual validation function for integer-based discrete logarithm parameters.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * Virtual function thunk for integer group parameter validation
 * Performs this pointer adjustment and delegates to actual validation function
 * @param a1 Adjusted this pointer for the DL_GroupParameters object
 * @param a2 Random number generator for validation
 * @param a3 Validation level flags
 * @return true if group parameters are valid, false otherwise
 */
char ValidateIntegerGroupParametersThunk(__int64 a1, __int64 a2, unsigned int a3)
{
    // Adjust this pointer using virtual table offset and call actual validation
    return CryptoPP::DL_GroupParameters<CryptoPP::Integer>::Validate(
        a1 - *((DWORD*)a1 - 4) - 192,
        a2,
        a3);
}



