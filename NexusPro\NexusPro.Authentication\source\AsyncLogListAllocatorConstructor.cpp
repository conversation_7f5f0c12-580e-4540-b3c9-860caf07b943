﻿/*
 * AsyncLogListAllocatorConstructor.cpp
 * Original Function: ??0?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@AEBV?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@1@@Z
 * Address: 0x1403C45B0
 *
 * STL list constructor for async log containers.
 * Initializes list with specified allocator for async log info pairs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <list>
#include <memory>
#include <utility>

/**
 * Constructor for list with allocator
 * Initializes list with specified allocator
 * @param this_ptr Pointer to the list object being constructed
 * @param _Al Reference to the allocator to use
 */
void AsyncLogListAllocatorConstructor(void* this_ptr, void* _Al)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    std::allocator<std::pair<int const, CAsyncLogInfo*>> v4; // al@4
    __int64 v5; // [sp+0h] [bp-38h]@1
    char v6; // [sp+20h] [bp-18h]@4
    std::allocator<std::pair<int const, CAsyncLogInfo*>>* v7; // [sp+28h] [bp-10h]@4
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* v8; // [sp+40h] [bp+8h]@1

    v8 = (std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)this_ptr;
    v2 = &v5;

    // Initialize debug pattern in local memory
    for (i = 12LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    v7 = (std::allocator<std::pair<int const, CAsyncLogInfo*>>*)&v6;

    // Initialize temporary allocator
    std::allocator<std::pair<int const, CAsyncLogInfo*>>::allocator(
        (std::allocator<std::pair<int const, CAsyncLogInfo*>>*)&v6,
        (std::allocator<std::pair<int const, CAsyncLogInfo*>>*)_Al);

    // Initialize list value with allocator
    std::_List_val<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_List_val(
        (std::_List_val<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)&v8->_Myfirstiter,
        v4);

    // Buy head node and initialize size
    v8->_Myhead = std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Buynode(v8);
    v8->_Mysize = 0LL;
}





