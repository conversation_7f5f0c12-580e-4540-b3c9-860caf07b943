﻿/*
 * AsyncLogListNodeConstructor.cpp
 * Original Function: ??0?$_List_nod@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@IEAA@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@1@@Z
 * Address: 0x1403C7520
 *
 * STL list node constructor for async log containers.
 * Initializes list node with allocator for async log info pairs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>
#include <utility>

/**
 * Constructor for list node with allocator
 * Initializes list node with specified allocator
 * @param this_ptr Pointer to the list node object being constructed
 * @param _Al Allocator for the list node
 */
void AsyncLogListNodeConstructor(void* this_ptr, __int64 _Al)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* v5; // [sp+30h] [bp+8h]@1
    std::allocator<std::pair<int const, CAsyncLogInfo*>>* __formal; // [sp+38h] [bp+10h]@1

    __formal = (std::allocator<std::pair<int const, CAsyncLogInfo*>>*)_Al;
    v5 = (std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)this_ptr;
    v2 = &v4;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    // Initialize container base for iterator management
    std::_Container_base::_Container_base((std::_Container_base*)&v5->_Myfirstiter);

    // Initialize node allocator
    std::allocator<std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node>::allocator(
        &v5->_Alnod,
        __formal);
}





