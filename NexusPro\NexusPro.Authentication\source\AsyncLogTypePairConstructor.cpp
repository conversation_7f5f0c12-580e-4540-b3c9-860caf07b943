﻿/*
 * AsyncLogTypePairConstructor.cpp
 * Original Function: std::pair constructor from ASYNC_LOG_TYPE pair
 * Original Address: 0x1403C7630
 *
 * Description: Constructor for std::pair<const int, CAsyncLogInfo*> from
 * std::pair<ASYNC_LOG_TYPE, CAsyncLogInfo*>. Converts enum type to int.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <utility>

// Constructor for std::pair<const int, CAsyncLogInfo*> from ASYNC_LOG_TYPE pair
// Converts ASYNC_LOG_TYPE enum to int and copies the pointer
std::pair<const int, CAsyncLogInfo*>::pair(const std::pair<ASYNC_LOG_TYPE, CAsyncLogInfo*>& _Right) {
    this->first = static_cast<int>(_Right.first);
    this->second = _Right.second;
}



