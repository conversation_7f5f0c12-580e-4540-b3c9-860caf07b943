﻿/*
 * AsyncLogHashMapDestructor2.cpp
 * Original Function: ??1?$_Hash@V?$_Hmap_traits@HPEAVCAsyncLogInfo@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@$0A@@stdext@@@stdext@@@XZ
 * Address: 0x1403C1860
 *
 * STL hash map destructor for async log containers.
 * Destroys hash map and cleans up vector and list components.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Additional STL includes for compilation
#include <memory>
#include <utility>

/**
 * Destructor for hash map
 * Destroys hash map and cleans up vector and list components
 * @param this_ptr Pointer to the hash map object being destroyed
 */
void AsyncLogHashMapDestructor2(void* this_ptr)
{
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v3; // [sp+0h] [bp-38h]@1
    __int64 v4; // [sp+20h] [bp-18h]@4
    stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>* v5; // [sp+40h] [bp+8h]@1

    v5 = (stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>*)this_ptr;
    v1 = &v3;

    // Initialize debug pattern in local memory
    for (i = 12LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }

    v4 = -2LL;

    // Destroy vector component
    std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>>::~vector(&v5->_Vec);

    // Destroy list component
    std::list<std::pair<int const, CAsyncLogInfo*>>::~list(&v5->_List);
}





