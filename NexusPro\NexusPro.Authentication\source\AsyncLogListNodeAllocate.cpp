﻿/*
 * AsyncLogListNodeAllocate.cpp
 * Original Function: std::_Allocate for AsyncLogInfo list nodes
 * Original Address: 0x1403C7E90
 *
 * Description: Allocates memory for list nodes containing AsyncLogInfo pairs.
 * Performs overflow checking and throws bad_alloc on allocation failure.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>
#include <utility>
#include <list>
#include <new>
#include <cstring>

// Allocates memory for list nodes containing AsyncLogInfo pairs
// Returns pointer to allocated node array or throws bad_alloc
template<typename NodeType>
NodeType* AsyncLogListNodeAllocate(size_t count, NodeType* /*unused*/) {
{
    // Initialize debug pattern for stack frame
    char debugBuffer[64];
    memset(debugBuffer, 0xCC, sizeof(debugBuffer));

    // Check for zero count
    if (count == 0) {
        return nullptr;
    }

    // Check for overflow: max_size / count < sizeof(NodeType)
    const size_t nodeSize = sizeof(NodeType);
    const size_t maxCount = SIZE_MAX / nodeSize;

    if (count > maxCount) {
        // Allocation would overflow, throw bad_alloc
        throw std::bad_alloc();
    }

    // Calculate total size needed
    size_t totalSize = count * nodeSize;

    // Allocate memory using operator new
    void* ptr = operator new(totalSize);

    return static_cast<NodeType*>(ptr);
}




