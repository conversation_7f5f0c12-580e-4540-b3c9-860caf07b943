﻿/*
 * AsyncLogBidirectionalIteratorAssignment.cpp
 * Original Function: ??4?$_Bidit@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@_JPEBU12@AEBU12@@std@@AEAU01@AEBU01@@Z
 * Address: 0x1403C2DF0
 *
 * STL bidirectional iterator assignment operator for async log containers.
 * Assigns one bidirectional iterator to another.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <utility>

/**
 * Assignment operator for bidirectional iterator
 * Assigns one bidirectional iterator to another
 * @param this_ptr Pointer to the iterator object being assigned to
 * @param __that Pointer to the iterator object being assigned from
 * @return Pointer to the assigned iterator
 */
std::_Bidit<std::pair<int const, CAsyncLogInfo*>, __int64, std::pair<int const, CAsyncLogInfo*> const*, std::pair<int const, CAsyncLogInfo*> const&>*
AsyncLogBidirectionalIteratorAssignment(
    std::_Bidit<std::pair<int const, CAsyncLogInfo*>, __int64, std::pair<int const, CAsyncLogInfo*> const*, std::pair<int const, CAsyncLogInfo*> const&>* this_ptr,
    std::_Bidit<std::pair<int const, CAsyncLogInfo*>, __int64, std::pair<int const, CAsyncLogInfo*> const*, std::pair<int const, CAsyncLogInfo*> const&>* __that)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-28h]@1
    std::_Bidit<std::pair<int const, CAsyncLogInfo*>, __int64, std::pair<int const, CAsyncLogInfo*> const*, std::pair<int const, CAsyncLogInfo*> const&>* v6; // [sp+30h] [bp+8h]@1

    v6 = this_ptr;
    v2 = &v5;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    // Assign iterator base
    std::_Iterator_base::operator=((std::_Iterator_base*)&v6->_Mycont, (std::_Iterator_base*)&__that->_Mycont);

    return v6;
}





