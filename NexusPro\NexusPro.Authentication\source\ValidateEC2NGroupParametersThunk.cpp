﻿/*
 * ValidateEC2NGroupParametersThunk.cpp
 * Original Function: CryptoPP::DL_GroupParameters<EC2NPoint>::Validate thunk
 * Original Address: 0x1405ADD90
 *
 * Description: Virtual function thunk for validating EC2N elliptic curve group parameters.
 * Adjusts the this pointer and calls the actual validation function.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Virtual function thunk for EC2N group parameter validation
// Adjusts this pointer and delegates to the actual validation function
bool CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>::ValidateThunk(
    CryptoPP::RandomNumberGenerator& rng,
    unsigned int level) const {

    // Adjust this pointer for virtual inheritance
    const CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>* adjustedThis =
        reinterpret_cast<const CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>*>(
            reinterpret_cast<const char*>(this) - 408);

    // Call the actual validation function
    return adjustedThis->Validate(rng, level);
}



