﻿/*
 * AsyncLogIteratorBoolPairDestructor.cpp
 * Original Function: ??1?$std::pair@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@_N@std@@@XZ
 * Address: 0x1403C1670
 *
 * STL pair destructor for async log iterator and boolean.
 * Destroys pair containing iterator and boolean value.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Destructor for pair with iterator and boolean
 * Destroys pair containing iterator and boolean value
 * @param this_ptr Pointer to the pair object being destroyed
 */
void AsyncLogIteratorBoolPairDestructor(void* this_ptr)
{
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v3; // [sp+0h] [bp-28h]@1
    void** v4; // [sp+30h] [bp+8h]@1

    v4 = (void**)this_ptr;
    v1 = &v3;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }

    // Destroy first element (iterator)
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::~_Iterator(&v4->first);
}





