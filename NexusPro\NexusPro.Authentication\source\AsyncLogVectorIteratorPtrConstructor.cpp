﻿/*
 * AsyncLogVectorIteratorPtrConstructor.cpp
 * Original Function: ??0?$_Vector_iterator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@@PEAV?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@1@@Z
 * Address: 0x1403C6D70
 *
 * STL vector iterator constructor for async log containers.
 * Initializes vector iterator with pointer to element.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Constructor for vector iterator with pointer
 * Initializes vector iterator with pointer to element
 * @param this_ptr Pointer to the vector iterator object being constructed
 * @param _Ptr Pointer to the element to initialize iterator with
 */
void AsyncLogVectorIteratorPtrConstructor(void* this_ptr, void* _Ptr)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    void** v5; // [sp+30h] [bp+8h]@1

    v5 = (void**)this_ptr;
    v2 = &v4;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    // Initialize vector iterator with pointer
    std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::_Vector_iterator(
        (std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>*)&v5->_Mycont,
        _Ptr);
}





