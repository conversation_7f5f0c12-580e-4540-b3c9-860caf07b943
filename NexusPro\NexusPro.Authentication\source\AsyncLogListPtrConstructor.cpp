﻿/*
 * AsyncLogListPtrConstructor.cpp
 * Original Function: ??0?$_List_ptr@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@IEAA@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@1@@Z
 * Address: 0x1403C6F70
 *
 * STL list pointer constructor for async log containers.
 * Initializes list pointer with allocator for async log info pairs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>
#include <utility>

/**
 * Constructor for list pointer with allocator
 * Initializes list pointer with specified allocator
 * @param this_ptr Pointer to the list pointer object being constructed
 * @param _Al Allocator for the list pointer
 */
void AsyncLogListPtrConstructor(void* this_ptr, __int64 _Al)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    std::allocator<std::pair<int const, CAsyncLogInfo*>> v4; // al@4
    __int64 v5; // [sp+0h] [bp-38h]@1
    char v6; // [sp+20h] [bp-18h]@4
    std::allocator<std::pair<int const, CAsyncLogInfo*>>* v7; // [sp+28h] [bp-10h]@4
    std::_List_ptr<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* v8; // [sp+40h] [bp+8h]@1
    std::allocator<std::pair<int const, CAsyncLogInfo*>>* __formal; // [sp+48h] [bp+10h]@1

    __formal = (std::allocator<std::pair<int const, CAsyncLogInfo*>>*)_Al;
    v8 = (std::_List_ptr<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)this_ptr;
    v2 = &v5;

    // Initialize debug pattern in local memory
    for (i = 12LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    v7 = (std::allocator<std::pair<int const, CAsyncLogInfo*>>*)&v6;

    // Initialize temporary allocator
    std::allocator<std::pair<int const, CAsyncLogInfo*>>::allocator(
        (std::allocator<std::pair<int const, CAsyncLogInfo*>>*)&v6,
        (std::allocator<std::pair<int const, CAsyncLogInfo*>>*)_Al);

    // Initialize list node with allocator
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_List_nod(
        (std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)&v8->_Myfirstiter,
        v4);

    // Initialize pointer allocator
    std::allocator<std::_List_ptr<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node*>::allocator(
        &v8->_Alptr,
        __formal);
}





