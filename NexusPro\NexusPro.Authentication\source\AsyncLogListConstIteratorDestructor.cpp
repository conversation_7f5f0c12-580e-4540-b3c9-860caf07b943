﻿/*
 * AsyncLogListConstIteratorDestructor.cpp
 * Original Function: ??1?$_Const_iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@XZ
 * Address: 0x1403C11F0
 *
 * STL list const iterator destructor for async log containers.
 * Destroys const iterator and cleans up iterator base.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Destructor for list const iterator
 * Destroys const iterator and cleans up iterator base
 * @param this_ptr Pointer to the iterator object being destroyed
 */
void AsyncLogListConstIteratorDestructor(void* this_ptr)
{
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v3; // [sp+0h] [bp-28h]@1
    void** v4; // [sp+30h] [bp+8h]@1

    v4 = (void**)this_ptr;
    v1 = &v3;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }

    // Destroy iterator base
    std::_Iterator_base::~_Iterator_base((std::_Iterator_base*)&v4->_Mycont);
}





