﻿/*
 * AsyncLogListConstIteratorConstructor.cpp
 * Original Function: std::list::_Const_iterator copy constructor for AsyncLogInfo pairs
 * Original Address: 0x1403C1480
 *
 * Description: Copy constructor for const iterator over AsyncLogInfo list.
 * Initializes bidirectional iterator base and copies node pointer.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>
#include <cstring>

// Copy constructor for list const iterator
// Initializes iterator from another const iterator of the same type
template<int Secure>
std::list<std::pair<const int, CAsyncLogInfo*>>::_Const_iterator<Secure>::_Const_iterator(
    const std::list<std::pair<const int, CAsyncLogInfo*>>::_Const_iterator<Secure>& other) {

    // Initialize debug pattern for stack frame
    char debugBuffer[32];
    memset(debugBuffer, 0xCC, sizeof(debugBuffer));

    // Initialize bidirectional iterator base
    std::_Bidit<std::pair<const int, CAsyncLogInfo*>, ptrdiff_t,
                const std::pair<const int, CAsyncLogInfo*>*,
                const std::pair<const int, CAsyncLogInfo*>&>::_Bidit(other);

    // Copy node pointer
    this->_Ptr = other._Ptr;
}




