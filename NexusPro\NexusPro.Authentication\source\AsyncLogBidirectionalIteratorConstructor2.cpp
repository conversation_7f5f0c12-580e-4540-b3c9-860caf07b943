﻿/*
 * AsyncLogBidirectionalIteratorConstructor2.cpp
 * Original Function: ??0?$_Bidit@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@_JPEBU12@AEBU12@@std@@@XZ
 * Address: 0x1403C5E20
 *
 * Bidirectional iterator constructor for async log containers.
 * Initializes bidirectional iterator for STL containers containing async log info pairs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <utility>

/**
 * Constructor for bidirectional iterator template specialization
 * Initializes iterator for async log pair containers
 * @param this Pointer to the iterator object being constructed
 */
void AsyncLogBidirectionalIteratorConstructor2(void* this_ptr)
{
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v3; // [sp+0h] [bp-28h]@1
    std::_Bidit<std::pair<int const, CAsyncLogInfo*>, __int64, std::pair<int const, CAsyncLogInfo*> const*, std::pair<int const, CAsyncLogInfo*> const&>* v4; // [sp+30h] [bp+8h]@1

    v4 = (std::_Bidit<std::pair<int const, CAsyncLogInfo*>, __int64, std::pair<int const, CAsyncLogInfo*> const*, std::pair<int const, CAsyncLogInfo*> const&>*)this_ptr;
    v1 = &v3;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }

    // Initialize iterator base container reference
    std::_Iterator_base::_Iterator_base((std::_Iterator_base*)&v4->_Mycont);
}




