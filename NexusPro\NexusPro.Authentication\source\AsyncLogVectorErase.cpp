﻿/*
 * AsyncLogVectorErase.cpp
 * Original Function: ?erase@?$std::vector@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@?AV?$_Vector_iterator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@2@V32@0@Z
 * Address: 0x1403C5070
 *
 * STL vector erase function for async log iterator containers.
 * Removes a range of elements from the vector and returns iterator to next element.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Erases a range of elements from the async log vector
 * Removes elements from _First to _Last and returns iterator to next element
 * @param result Pointer to store the result iterator
 * @param this_ptr Pointer to the vector object
 * @param _First Iterator to the first element to erase
 * @param _Last Iterator to the element after the last element to erase
 * @return Iterator to the element following the erased range
 */
void* AsyncLogVectorErase(void* result, std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>* this_ptr, void* _First, void* _Last)
{
    __int64* v4; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v7; // [sp+0h] [bp-48h]@1
    void** _Firsta; // [sp+20h] [bp-28h]@5
    int v9; // [sp+28h] [bp-20h]@4
    __int64 v10; // [sp+30h] [bp-18h]@4
    void** v11; // [sp+50h] [bp+8h]@1
    void** v12; // [sp+58h] [bp+10h]@1
    void** v13; // [sp+60h] [bp+18h]@1
    void** _Right; // [sp+68h] [bp+20h]@1

    _Right = (void**)_Last;
    v13 = (void**)_First;
    v12 = (void**)result;
    v11 = (void**)this_ptr;
    v4 = &v7;

    // Initialize debug pattern in local memory
    for (i = 16LL; i; --i) {
        *(DWORD*)v4 = 0xCCCCCCCC;
        v4 = (__int64*)((char*)v4 + 4);
    }

    v10 = -2LL;
    v9 = 0;

    // Only erase if range is not empty
    if (std::_Vector_iterator<>::operator!=(
        (std::_Vector_iterator<>*)&v13->_Mycont,
        (std::_Vector_iterator<>*)&_Right->_Mycont)) {

        // Copy elements after the range to fill the gap
        _Firsta = stdext::unchecked_copy<std::_Vector_iterator<>*>(
            _Right->_Myptr,
            v11->_Mylast,
            v13->_Myptr);

        // Destroy elements that are no longer needed
        std::_Vector_iterator<>::_Destroy(
            v11,
            _Firsta,
            v11->_Mylast);

        // Update vector end
        v11->_Mylast = _Firsta;
    }

    // Construct result iterator
    std::_Vector_iterator<>::_Vector_iterator<>(v12, v13);
    v9 |= 1u;

    // Cleanup temporary iterators
    std::_Vector_iterator<>::~_Vector_iterator<>(v13);
    std::_Vector_iterator<>::~_Vector_iterator<>(_Right);

    return v12;
}





