﻿/*
 * AsyncLogListConstIteratorAssignment.cpp
 * Original Function: ??4?$_Const_iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@AEAV012@AEBV012@@Z
 * Address: 0x1403C2D80
 *
 * STL list const iterator assignment operator for async log containers.
 * Assigns one const iterator to another.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Assignment operator for list const iterator
 * Assigns one const iterator to another
 * @param this_ptr Pointer to the iterator object being assigned to
 * @param __that Pointer to the iterator object being assigned from
 * @return Pointer to the assigned iterator
 */
std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>*
AsyncLogListConstIteratorAssignment(
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>* this_ptr,
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>* __that)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-28h]@1
    void** v6; // [sp+30h] [bp+8h]@1
    void** __thata; // [sp+38h] [bp+10h]@1

    __thata = (void**)__that;
    v6 = (void**)this_ptr;
    v2 = &v5;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    // Assign bidirectional iterator base
    std::_Bidit<std::pair<int const, CAsyncLogInfo*>, __int64, std::pair<int const, CAsyncLogInfo*> const*, std::pair<int const, CAsyncLogInfo*> const&>::operator=(
        (std::_Bidit<std::pair<int const, CAsyncLogInfo*>, __int64, std::pair<int const, CAsyncLogInfo*> const*, std::pair<int const, CAsyncLogInfo*> const&>*)&v6->_Mycont,
        (std::_Bidit<std::pair<int const, CAsyncLogInfo*>, __int64, std::pair<int const, CAsyncLogInfo*> const*, std::pair<int const, CAsyncLogInfo*> const&>*)&__that->_Mycont);

    // Assign pointer
    v6->_Ptr = __thata->_Ptr;

    return (std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>*)v6;
}





