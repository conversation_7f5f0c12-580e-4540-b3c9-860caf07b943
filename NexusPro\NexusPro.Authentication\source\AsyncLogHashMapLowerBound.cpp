﻿/*
 * AsyncLogHashMapLowerBound.cpp
 * Original Function: stdext::_Hash::lower_bound for AsyncLogInfo hash map
 * Original Address: 0x1403C30D0
 *
 * Description: Finds the first element not less than the given key in the hash map.
 * Part of the STL hash map implementation for AsyncLogInfo storage.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>
#include <vector>


// Hash map lower_bound function for AsyncLogInfo
// Finds the first element not less than the given key
template<typename Iterator>
Iterator AsyncLogHashMap::lower_bound(const int& key) {
    // Calculate hash position for the key
    size_t hashPos = this->hash_function(key);

    // Get iterator to the bucket
    Iterator bucketIter = this->get_bucket_iterator(hashPos);
    Iterator endIter = this->get_bucket_end(hashPos);

    // Search through the bucket for the first element not less than key
    while (bucketIter != endIter) {
        const auto& currentPair = *bucketIter;

        // If current key is not less than search key, we found our position
        if (!this->key_compare(currentPair.first, key)) {
            // Check if this is exactly what we're looking for
            if (!this->key_compare(key, currentPair.first)) {
                return bucketIter;  // Found exact match
            } else {
                return this->end(); // Key would be inserted here but doesn't exist
            }
        }

        ++bucketIter;
    }

    // Key not found in this bucket, return end iterator
    return this->end();
}




