﻿/*
 * AsyncLogHashMapHashval.cpp
 * Original Function: ?_Hashval@?$_Hash@V?$_Hmap_traits@HPEAVCAsyncLogInfo@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@$0A@@stdext@@@stdext@@IEBA_KAEBH@Z
 * Address: 0x1403C3530
 *
 * STL hash map hash value calculation for async log containers.
 * Computes hash bucket index for async log info key-value pairs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>
#include <utility>

/**
 * Calculates hash bucket index for async log hash map
 * Computes the bucket index using hash function and mask
 * @param this_ptr Pointer to the hash map object
 * @param _Keyval Pointer to the key value to hash
 * @return Hash bucket index
 */
unsigned __int64 AsyncLogHashMapHashval(stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>* this_ptr, const int* _Keyval)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-38h]@1
    unsigned __int64 v6; // [sp+20h] [bp-18h]@4
    stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>* v7; // [sp+40h] [bp+8h]@1

    v7 = this_ptr;
    v2 = &v5;

    // Initialize debug pattern in local memory
    for (i = 12LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    // Calculate hash value using mask and comparison function
    v6 = v7->_Mask & stdext::hash_compare<int, std::less<int>>::operator()(&v7->comp, _Keyval);

    // Adjust hash value if it exceeds maximum index
    if (v7->_Maxidx <= v6) {
        v6 -= (v7->_Mask >> 1) + 1;
    }

    return v6;
}




