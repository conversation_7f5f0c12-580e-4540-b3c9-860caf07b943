﻿/*
 * AsyncLogListConstIteratorConstructor2.cpp
 * Original Function: ??0?$_Const_iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@XZ
 * Address: 0x1403C5B90
 *
 * Const iterator constructor for async log list containers.
 * Initializes const iterator for STL list containing async log info pairs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Constructor for const iterator template specialization
 * Initializes const iterator for async log list containers
 * @param this Pointer to the const iterator object being constructed
 */
void AsyncLogListConstIteratorConstructor2(void* this_ptr)
{
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v3; // [sp+0h] [bp-28h]@1
    void** v4; // [sp+30h] [bp+8h]@1

    v4 = (void**)this_ptr;
    v1 = &v3;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }

    // Initialize iterator base container reference
    std::_Iterator_base::_Iterator_base((std::_Iterator_base*)&v4->_Mycont);

    // Initialize pointer to null
    v4->_Ptr = 0LL;
}




