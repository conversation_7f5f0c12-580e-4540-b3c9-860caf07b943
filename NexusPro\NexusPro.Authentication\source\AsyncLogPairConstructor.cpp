﻿/*
 * AsyncLogPairConstructor.cpp
 * Original Function: std::pair<int,CAsyncLogInfo*> copy constructor
 * Original Address: 0x1403C7590
 *
 * Description: Copy constructor for std::pair containing AsyncLogInfo pointer.
 * Creates a new pair from an existing const pair reference.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <utility>

// Copy constructor for std::pair<int, CAsyncLogInfo*>
// Creates a new pair from a const pair reference
std::pair<int, CAsyncLogInfo*>::pair(const std::pair<int, CAsyncLogInfo*>& _Right) {
    this->first = _Right.first;
    this->second = _Right.second;
}



