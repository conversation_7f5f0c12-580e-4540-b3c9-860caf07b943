/*
 * ValidateIntegerGroupParameters.cpp
 * Original Function: CryptoPP::DL_GroupParameters<Integer>::Validate
 * Original Address: 0x140551AC0
 *
 * Description: Validates Integer-based discrete logarithm group parameters.
 * Performs cryptographic validation of integer group parameters for DSA/DH.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Validates Integer-based discrete logarithm group parameters
// Returns true if parameters are valid, false otherwise
bool CryptoPP::DL_GroupParameters<CryptoPP::Integer>::Validate(
    CryptoPP::RandomNumberGenerator& rng,
    unsigned int level) const {
    // Get the integer group implementation
    const CryptoPP::IntegerGroup* group = this->GetGroup();
    if (!group) {
        return false;
    }

    // Check if group is valid
    if (!group->IsValid()) {
        return false;
    }

    // Check if validation level is already satisfied
    if (this->m_validationLevel > level) {
        return true;
    }

    // Perform group parameter validation
    bool groupValid = group->ValidateParameters(rng, level);

    if (groupValid) {
        // Validate the generator and order
        const CryptoPP::Integer& generator = this->GetGenerator();
        const CryptoPP::Integer& order = this->GetOrder();

        bool generatorValid = group->ValidateElement(generator, rng, level);

        if (generatorValid) {
            // Update validation level on success
            this->m_validationLevel = level + 1;
        } else {
            this->m_validationLevel = 0;
        }

        return generatorValid;
    }

    // Validation failed
    this->m_validationLevel = 0;
    return false;
}
}




