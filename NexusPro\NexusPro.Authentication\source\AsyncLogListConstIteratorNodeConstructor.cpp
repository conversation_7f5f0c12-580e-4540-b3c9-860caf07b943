﻿/*
 * AsyncLogListConstIteratorNodeConstructor.cpp
 * Original Function: ??0?$_Const_iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@PEAU_Node@?$_List_nod@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@2@@Z
 * Address: 0x1403C6D10
 *
 * Const iterator constructor with node parameter for async log list containers.
 * Initializes const iterator with specific list node for async log info pairs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>

/**
 * Constructor for const iterator with node parameter
 * Initializes const iterator pointing to specific list node
 * @param this_ptr Pointer to the const iterator object being constructed
 * @param _Pnode Pointer to the list node to initialize iterator with
 */
void AsyncLogListConstIteratorNodeConstructor(void* this_ptr, std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node* _Pnode)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    void** v5; // [sp+30h] [bp+8h]@1
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node* v6; // [sp+38h] [bp+10h]@1

    v6 = _Pnode;
    v5 = (void**)this_ptr;
    v2 = &v4;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    // Initialize iterator base container reference
    std::_Iterator_base::_Iterator_base((std::_Iterator_base*)&v5->_Mycont);

    // Set iterator pointer to the specified node
    v5->_Ptr = v6;
}




