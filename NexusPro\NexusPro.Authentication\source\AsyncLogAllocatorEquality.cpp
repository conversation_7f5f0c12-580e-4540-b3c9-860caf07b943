﻿/*
 * AsyncLogAllocatorEquality.cpp
 * Original Function: ??$?8U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@U01@@std@@YA_NAEBV?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@0@0@Z
 * Address: 0x1403C7690
 *
 * STL allocator equality operator for async log pair containers.
 * Compares two allocators for equality.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>
#include <utility>

/**
 * Equality operator for async log pair allocators
 * Compares two allocators for equality
 * @param __formal First allocator to compare
 * @param a2 Second allocator to compare
 * @return true if allocators are equal (always true for standard allocators)
 */
bool AsyncLogAllocatorEquality(
    const std::allocator<std::pair<int const, CAsyncLogInfo*>>* __formal,
    const std::allocator<std::pair<int const, CAsyncLogInfo*>>* a2)
{
    // Standard allocators are always equal
    return true;
}




