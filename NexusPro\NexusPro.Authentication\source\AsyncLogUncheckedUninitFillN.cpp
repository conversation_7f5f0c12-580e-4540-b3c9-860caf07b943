﻿/*
 * AsyncLogUncheckedUninitFillN.cpp
 * Original Function: stdext::unchecked_uninitialized_fill_n for AsyncLogInfo iterators
 * Original Address: 0x1403C7C50
 *
 * Description: Uninitialized fill operation for AsyncLogInfo list iterators.
 * Fills a range of uninitialized memory with copies of a given value.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>
#include <cstring>


// Unchecked uninitialized fill_n for AsyncLogInfo list iterators
// Fills count elements starting at first with copies of val
template<typename Iterator, typename Size, typename T, typename Allocator>
void AsyncLogUncheckedUninitFillN(Iterator first, Size count, const T& val, Allocator& alloc) {
    // Initialize debug pattern for stack frame
    char debugBuffer[64];
    memset(debugBuffer, 0xCC, sizeof(debugBuffer));

    // Perform uninitialized fill operation
    Iterator current = first;
    for (Size i = 0; i < count; ++i) {
        // Use allocator to construct object in place
        std::allocator_traits<Allocator>::construct(alloc, &(*current), val);
        ++current;
    }
}




