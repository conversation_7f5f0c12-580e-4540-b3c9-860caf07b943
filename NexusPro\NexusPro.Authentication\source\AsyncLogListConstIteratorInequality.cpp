﻿/*
 * AsyncLogListConstIteratorInequality.cpp
 * Original Function: ??9?$_Const_iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@QEBA_NAEBV012@@Z
 * Address: 0x1403C2C50
 *
 * STL list const iterator inequality operator for async log containers.
 * Compares two const iterators for inequality.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Inequality operator for list const iterator
 * Compares two const iterators for inequality
 * @param this_ptr Pointer to the first iterator
 * @param _Right Pointer to the second iterator
 * @return true if iterators point to different elements
 */
bool AsyncLogListConstIteratorInequality(
    const std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>* this_ptr,
    const std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>* _Right)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-38h]@1
    void** v6; // [sp+40h] [bp+8h]@1

    v6 = (void**)this_ptr;
    v2 = &v5;

    // Initialize debug pattern in local memory
    for (i = 12LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    // Return negation of equality comparison
    return std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>::operator==(
        (std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>*)v6,
        _Right) == 0;
}





