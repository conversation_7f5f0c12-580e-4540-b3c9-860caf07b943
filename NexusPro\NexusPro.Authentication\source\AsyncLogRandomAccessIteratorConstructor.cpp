﻿/*
 * AsyncLogRandomAccessIteratorConstructor.cpp
 * Original Function: ??0?$_Ranit@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@_JPEBV123@AEBV123@@std@@@AEBU01@@Z
 * Address: 0x1403C5EE0
 *
 * STL random access iterator constructor for async log containers.
 * Initializes random access iterator by copying from another iterator.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Copy constructor for random access iterator
 * Initializes random access iterator by copying from another iterator
 * @param this_ptr Pointer to the iterator object being constructed
 * @param __that Reference to the iterator to copy from
 */
void AsyncLogRandomAccessIteratorConstructor(void* this_ptr, void* __that)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    void** v5; // [sp+30h] [bp+8h]@1

    v5 = (void**)this_ptr;
    v2 = &v4;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    // Initialize iterator base with container from source
    std::_Iterator_base::_Iterator_base(
        (std::_Iterator_base*)&v5->_Mycont,
        (std::_Iterator_base*)&((void**)__that)->_Mycont);
}





